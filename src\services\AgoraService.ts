import AgoraRTC, { 
  IAgoraRTCClient, 
  ILocalVideoTrack, 
  ILocalAudioTrack, 
  IRemoteVideoTrack, 
  IRemoteAudioTrack,
  ICameraVideoTrack,
  IMicrophoneAudioTrack
} from "agora-rtc-sdk-ng";

export interface AgoraConfig {
  appId: string;
  tempToken?: string;
  channel: string;
  uid: number;
}

export interface RemoteUser {
  uid: number;
  videoTrack?: IRemoteVideoTrack;
  audioTrack?: IRemoteAudioTrack;
  hasVideo: boolean;
  hasAudio: boolean;
}

export class AgoraService {
  private client: IAgoraRTCClient;
  private localVideoTrack: ILocalVideoTrack | null = null;
  private localAudioTrack: ILocalAudioTrack | null = null;
  private remoteUsers: Map<number, RemoteUser> = new Map();
  private isJoined = false;
  private currentFacingMode: "user" | "environment" = "user";

  // Event callbacks
  public onUserJoined?: (user: RemoteUser) => void;
  public onUserLeft?: (uid: number) => void;
  public onUserPublished?: (user: RemoteUser, mediaType: "video" | "audio") => void;
  public onUserUnpublished?: (user: RemoteUser, mediaType: "video" | "audio") => void;
  public onConnectionStateChanged?: (state: string) => void;

  constructor() {
    // Create Agora client
    this.client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.client.on("user-published", async (user, mediaType) => {
      // Only handle video and audio, ignore datachannel
      if (mediaType !== "video" && mediaType !== "audio") return;
      
      await this.client.subscribe(user, mediaType);
      
      let remoteUser = this.remoteUsers.get(user.uid as number);
      if (!remoteUser) {
        remoteUser = {
          uid: user.uid as number,
          hasVideo: false,
          hasAudio: false,
        };
        this.remoteUsers.set(user.uid as number, remoteUser);
      }

      if (mediaType === "video") {
        remoteUser.videoTrack = user.videoTrack;
        remoteUser.hasVideo = true;
      } else if (mediaType === "audio") {
        remoteUser.audioTrack = user.audioTrack;
        remoteUser.hasAudio = true;
        user.audioTrack?.play();
      }

      this.onUserPublished?.(remoteUser, mediaType);
    });

    this.client.on("user-unpublished", (user, mediaType) => {
      // Only handle video and audio, ignore datachannel
      if (mediaType !== "video" && mediaType !== "audio") return;
      
      const remoteUser = this.remoteUsers.get(user.uid as number);
      if (remoteUser) {
        if (mediaType === "video") {
          remoteUser.hasVideo = false;
          remoteUser.videoTrack = undefined;
        } else if (mediaType === "audio") {
          remoteUser.hasAudio = false;
          remoteUser.audioTrack = undefined;
        }
        this.onUserUnpublished?.(remoteUser, mediaType);
      }
    });

    this.client.on("user-joined", (user) => {
      const remoteUser: RemoteUser = {
        uid: user.uid as number,
        hasVideo: false,
        hasAudio: false,
      };
      this.remoteUsers.set(user.uid as number, remoteUser);
      this.onUserJoined?.(remoteUser);
    });

    this.client.on("user-left", (user) => {
      this.remoteUsers.delete(user.uid as number);
      this.onUserLeft?.(user.uid as number);
    });

    this.client.on("connection-state-change", (state) => {
      this.onConnectionStateChanged?.(state);
    });
  }

  // Generate temporary token (for demo - in production, get from your backend)
  public generateTempToken(appId: string, channel: string, uid: number): string {
    // This is a demo implementation
    // In production, you should call your backend to generate tokens
    const currentTime = Math.floor(Date.now() / 1000);
    const expireTime = currentTime + 3600; // 1 hour
    
    // For demo purposes, return a placeholder token
    // In real implementation, use your Agora token server
    return `temp_token_${appId}_${channel}_${uid}_${expireTime}`;
  }

  public async joinChannel(config: AgoraConfig): Promise<void> {
    try {
      // If already joined, leave first
      if (this.isJoined) {
        console.log("Already joined, leaving channel first...");
        await this.leaveChannel();
      }

      // Check client connection state
      const connectionState = this.client.connectionState;
      if (connectionState === "CONNECTING" || connectionState === "CONNECTED") {
        console.log("Client is already connecting/connected, leaving first...");
        await this.client.leave();
        this.isJoined = false;
        this.remoteUsers.clear();
      }

      // Generate temp token if not provided
      const token = config.tempToken || this.generateTempToken(config.appId, config.channel, config.uid);

      console.log("Joining channel with config:", { appId: config.appId, channel: config.channel, uid: config.uid });
      await this.client.join(config.appId, config.channel, token, config.uid);
      this.isJoined = true;
      console.log("Successfully joined channel");
    } catch (error) {
      console.error("Failed to join channel:", error);
      this.isJoined = false;
      throw error;
    }
  }

  public async leaveChannel(): Promise<void> {
    try {
      const connectionState = this.client.connectionState;
      if (this.isJoined || connectionState === "CONNECTED" || connectionState === "CONNECTING") {
        console.log("Leaving channel...");
        await this.client.leave();
        console.log("Successfully left channel");
      }
    } catch (error) {
      console.error("Error leaving channel:", error);
      // Continue with cleanup even if leave fails
    } finally {
      this.isJoined = false;
      this.remoteUsers.clear();
    }
  }

  public async createLocalTracks(videoConfig?: { facingMode?: "user" | "environment" }): Promise<void> {
    try {
      // Create audio track
      this.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
      
      // Create video track with camera facing mode
      this.localVideoTrack = await AgoraRTC.createCameraVideoTrack({
        facingMode: videoConfig?.facingMode || "user",
      });
    } catch (error) {
      console.error("Failed to create local tracks:", error);
      throw error;
    }
  }

  public async publishTracks(): Promise<void> {
    if (this.localAudioTrack && this.localVideoTrack && this.isJoined) {
      await this.client.publish([this.localAudioTrack, this.localVideoTrack]);
    }
  }

  public async unpublishTracks(): Promise<void> {
    if (this.isJoined) {
      await this.client.unpublish();
    }
  }

  public async switchCamera(): Promise<void> {
    if (this.localVideoTrack) {
      try {
        const devices = await AgoraRTC.getCameras();
        if (devices.length > 1) {
          // Toggle facing mode
          this.currentFacingMode = this.currentFacingMode === "user" ? "environment" : "user";
          
          this.localVideoTrack.close();
          this.localVideoTrack = await AgoraRTC.createCameraVideoTrack({
            facingMode: this.currentFacingMode,
          });
          
          // Re-publish if we were publishing
          if (this.isJoined) {
            await this.client.unpublish();
            await this.client.publish([this.localAudioTrack!, this.localVideoTrack]);
          }
        }
      } catch (error) {
        console.error("Failed to switch camera:", error);
      }
    }
  }

  public getCurrentFacingMode(): "user" | "environment" {
    return this.currentFacingMode;
  }

  public async toggleMicrophone(): Promise<boolean> {
    if (this.localAudioTrack) {
      const enabled = this.localAudioTrack.enabled;
      await this.localAudioTrack.setEnabled(!enabled);
      return !enabled;
    }
    return false;
  }

  public async toggleCamera(): Promise<boolean> {
    if (this.localVideoTrack) {
      const enabled = this.localVideoTrack.enabled;
      await this.localVideoTrack.setEnabled(!enabled);
      return !enabled;
    }
    return false;
  }

  public playLocalVideo(element: HTMLElement): void {
    if (this.localVideoTrack) {
      this.localVideoTrack.play(element);
    }
  }

  public playRemoteVideo(uid: number, element: HTMLElement): void {
    const user = this.remoteUsers.get(uid);
    if (user?.videoTrack) {
      user.videoTrack.play(element);
    }
  }

  public getRemoteUsers(): RemoteUser[] {
    return Array.from(this.remoteUsers.values());
  }

  public getLocalTracks() {
    return {
      videoTrack: this.localVideoTrack,
      audioTrack: this.localAudioTrack,
    };
  }

  public getConnectionState(): string {
    return this.client.connectionState;
  }

  public isClientJoined(): boolean {
    return this.isJoined && this.client.connectionState === "CONNECTED";
  }

  public async destroy(): Promise<void> {
    if (this.localVideoTrack) {
      this.localVideoTrack.close();
      this.localVideoTrack = null;
    }
    if (this.localAudioTrack) {
      this.localAudioTrack.close();
      this.localAudioTrack = null;
    }
    await this.leaveChannel();
  }
}