// Agora configuration for the Wolf Call System
export const AGORA_CONFIG = {
  // Agora App ID - provided by <PERSON><PERSON><PERSON> Console
  APP_ID: "8f61c340d00d408997dfa023554e19258f61c340d00d408997dfa023554e1925",
  
  // Channel name for the call
  CHANNEL_NAME: "wolf",
  
  // Temporary token - in production, this should be generated by your backend
  TEMP_TOKEN: "007eJxTYJBWkOKdvErw8eQDjzziXLeX5Ca5pLP/vLXjaoCa+c+ZN+QUGCzSzAyTjU0MUgwMUkwMLCwtzVPSEg6MjE1NTVINLY1MZ0n0ZDQEMjKc/5rAysgAgSA+C0N5fk4aAwMA4vceYg==",
  
  // Default token expiry time (1 hour)
  DEFAULT_TOKEN_EXPIRY: 3600,
  
  // UID range for random generation
  UID_RANGE: {
    MIN: 1000,
    MAX: 10000
  }
} as const;

// Helper function to generate a random UID
export const generateRandomUID = (): number => {
  return Math.floor(Math.random() * (AGORA_CONFIG.UID_RANGE.MAX - AGORA_CONFIG.UID_RANGE.MIN)) + AGORA_CONFIG.UID_RANGE.MIN;
};

// Helper function to get Agora configuration
export const getAgoraConfig = () => ({
  appId: AGORA_CONFIG.APP_ID,
  channelName: AGORA_CONFIG.CHANNEL_NAME,
  channel: AGORA_CONFIG.CHANNEL_NAME,
  uid: generateRandomUID(),
  tempToken: AGORA_CONFIG.TEMP_TOKEN,
});
