import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ParticipantGrid } from "@/components/ParticipantGrid";
import { AgoraService, RemoteUser } from "@/services/AgoraService";
import { TokenService } from "@/services/TokenService";
import { getAgoraConfig } from "@/config/agora";
import { 
  Phone, 
  PhoneOff, 
  Mic, 
  MicOff, 
  Camera, 
  CameraOff, 
  RotateCcw,
  Users,
  Settings,
  Monitor,
  Smartphone,
  Wifi,
  WifiOff,
  Clock
} from "lucide-react";

interface CallInterfaceProps {
  onLogout: () => void;
}

export const CallInterface = ({ onLogout }: CallInterfaceProps) => {
  // Call state
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOn, setIsCameraOn] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<string>("DISCONNECTED");
  const [isConnecting, setIsConnecting] = useState(false);
  
  // Agora state
  const [agoraService] = useState(() => new AgoraService());
  const [remoteUsers, setRemoteUsers] = useState<RemoteUser[]>([]);
  const [currentToken, setCurrentToken] = useState<string>("");
  const [tokenExpiry, setTokenExpiry] = useState<number | null>(null);
  
  // Refs
  const localVideoRef = useRef<HTMLDivElement>(null);
  
  // Demo configuration - in production, get from environment/user settings
  const agoraConfig = {
    appId: "8f61c340d00d408997dfa023554e19258f61c340d00d408997dfa023554e1925", // Agora App ID
    channelName: "wolf",
    channel: "wolf",
    uid: Math.floor(Math.random() * 10000) + 1000, // Generate random UID
    tempToken: "007eJxTYJBWkOKdvErw8eQDjzziXLeX5Ca5pLP/vLXjaoCoCa+c+ZN+QUGCzSzAyTjU0MUgwwMUkwMLCwtzVPSEg6MjE1NTVINLY1MZ0n0ZDQEMjKc/5rAysgAgSA+C0N5fk4aAwMA4vceYg==", // Provided temp token
  };

  // Setup Agora event handlers
  useEffect(() => {
    agoraService.onUserJoined = (user: RemoteUser) => {
      setRemoteUsers(prev => [...prev, user]);
    };

    agoraService.onUserLeft = (uid: number) => {
      setRemoteUsers(prev => prev.filter(user => user.uid !== uid));
    };

    agoraService.onUserPublished = (user: RemoteUser) => {
      setRemoteUsers(prev => 
        prev.map(u => u.uid === user.uid ? user : u)
      );
    };

    agoraService.onUserUnpublished = (user: RemoteUser) => {
      setRemoteUsers(prev => 
        prev.map(u => u.uid === user.uid ? user : u)
      );
    };

    agoraService.onConnectionStateChanged = (state: string) => {
      setConnectionState(state);
      setIsConnected(state === "CONNECTED");
    };

    return () => {
      agoraService.destroy();
    };
  }, [agoraService]);

  // Play local video when camera is on
  useEffect(() => {
    if (isCameraOn && localVideoRef.current && agoraService) {
      agoraService.playLocalVideo(localVideoRef.current);
    }
  }, [isCameraOn, agoraService]);

  // Token renewal check
  useEffect(() => {
    if (currentToken && isCallActive) {
      const interval = setInterval(() => {
        if (TokenService.needsRenewal(currentToken)) {
          const newToken = TokenService.renewToken(agoraConfig);
          setCurrentToken(newToken);
          setTokenExpiry(TokenService.getTokenExpiration(newToken));
        }
      }, 60000); // Check every minute

      return () => clearInterval(interval);
    }
  }, [currentToken, isCallActive, agoraConfig]);

  const handleCall = async () => {
    if (!isCallActive) {
      try {
        // Prevent multiple join attempts
        if (isConnecting || agoraService.getConnectionState() === "CONNECTING") {
          console.log("Already connecting, please wait...");
          return;
        }

        setIsConnecting(true);
        setConnectionState("CONNECTING");

        // Use provided token
        const token = agoraConfig.tempToken;
        setCurrentToken(token);
        // Set a default expiry time since we're using a provided token
        setTokenExpiry(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now

        // Join channel
        await agoraService.joinChannel({
          ...agoraConfig,
          tempToken: token,
        });

        // Create local tracks
        await agoraService.createLocalTracks({
          facingMode: agoraService.getCurrentFacingMode(),
        });

        // Start with camera and microphone
        setIsCameraOn(true);
        setIsMuted(false);

        // Publish tracks
        await agoraService.publishTracks();

        setIsCallActive(true);
        setIsConnected(true);
        setConnectionState("CONNECTED");
      } catch (error) {
        console.error("Failed to start call:", error);
        setConnectionState("DISCONNECTED");
        setIsCallActive(false);
        setIsConnected(false);
      } finally {
        setIsConnecting(false);
      }
    } else {
      try {
        setIsConnecting(true);
        setConnectionState("DISCONNECTING");
        await agoraService.leaveChannel();
        setIsCallActive(false);
        setIsCameraOn(false);
        setIsMuted(false);
        setRemoteUsers([]);
        setIsConnected(false);
        setConnectionState("DISCONNECTED");
        setCurrentToken("");
        setTokenExpiry(null);
      } catch (error) {
        console.error("Failed to end call:", error);
        // Force reset state even if leave fails
        setIsCallActive(false);
        setIsCameraOn(false);
        setIsMuted(false);
        setRemoteUsers([]);
        setIsConnected(false);
        setConnectionState("DISCONNECTED");
        setCurrentToken("");
        setTokenExpiry(null);
      } finally {
        setIsConnecting(false);
      }
    }
  };

  const toggleMute = async () => {
    if (agoraService) {
      const newMutedState = await agoraService.toggleMicrophone();
      setIsMuted(!newMutedState);
    }
  };

  const toggleCamera = async () => {
    if (agoraService) {
      const newCameraState = await agoraService.toggleCamera();
      setIsCameraOn(newCameraState);
    }
  };

  const switchCamera = async () => {
    if (agoraService && isCameraOn) {
      try {
        await agoraService.switchCamera();
        // Re-play local video after camera switch
        if (localVideoRef.current) {
          setTimeout(() => {
            agoraService.playLocalVideo(localVideoRef.current!);
          }, 500);
        }
      } catch (error) {
        console.error("Failed to switch camera:", error);
      }
    }
  };

  const handleParticipantVideoPlay = (uid: number, element: HTMLElement) => {
    agoraService.playRemoteVideo(uid, element);
  };

  const totalParticipants = remoteUsers.length + 1; // +1 for local user

  return (
    <div className="min-h-screen bg-background p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl md:text-4xl font-black text-foreground">
            WOLF CALL SYSTEM
          </h1>
          <div className="flex items-center gap-4 mt-2">
            <p className="text-muted-foreground font-bold">
              Multi-Device Agora Calling Platform
            </p>
            
            {/* Connection Status */}
            <div className={`flex items-center gap-2 px-3 py-1 border-2 border-border ${
              isConnected ? 'bg-success text-success-foreground' : 'bg-destructive text-destructive-foreground'
            }`}>
              {isConnected ? <Wifi className="w-4 h-4" /> : <WifiOff className="w-4 h-4" />}
              <span className="text-xs font-bold">{connectionState}</span>
            </div>

            {/* Token Status */}
            {currentToken && tokenExpiry && (
              <div className="flex items-center gap-2 px-3 py-1 border-2 border-border bg-card text-card-foreground">
                <Clock className="w-4 h-4" />
                <span className="text-xs font-bold">
                  TOKEN: {Math.max(0, Math.floor((tokenExpiry - Date.now() / 1000) / 60))}m
                </span>
              </div>
            )}
          </div>
        </div>
        <Button variant="outline" onClick={onLogout}>
          LOGOUT
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Area with Spaceship Grid */}
        <div className="lg:col-span-2">
          <Card className="border-4 border-border bg-card p-6">
            <div className="aspect-video bg-background border-2 border-border relative">
              <ParticipantGrid
                participants={remoteUsers}
                localVideoRef={localVideoRef}
                onParticipantVideoPlay={handleParticipantVideoPlay}
                maxParticipants={12}
              />
              
              {/* Call Status Overlay */}
              {isCallActive && (
                <div className="absolute top-4 right-4 bg-success text-success-foreground px-4 py-2 border-2 border-border">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-success-foreground rounded-full animate-pulse"></div>
                    <span className="font-bold">LIVE - {totalParticipants} PILOTS</span>
                  </div>
                </div>
              )}

              {/* Device Compatibility Indicators */}
              <div className="absolute bottom-4 right-4 flex gap-2">
                <div className="bg-card text-card-foreground p-2 border-2 border-border">
                  <Monitor className="w-4 h-4" />
                </div>
                <div className="bg-card text-card-foreground p-2 border-2 border-border">
                  <Smartphone className="w-4 h-4" />
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Enhanced Control Panel */}
        <div className="space-y-6">
          {/* Primary Call Controls */}
          <Card className="border-4 border-border bg-card p-6">
            <h3 className="text-xl font-black mb-4">MISSION CONTROL</h3>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              {/* Call/End Call Button */}
              <Button
                variant={isCallActive ? "destructive" : "call"}
                size="control"
                onClick={handleCall}
                className="col-span-2"
              >
                {isCallActive ? (
                  <>
                    <PhoneOff className="w-6 h-6 mr-2" />
                    END MISSION
                  </>
                ) : (
                  <>
                    <Phone className="w-6 h-6 mr-2" />
                    START MISSION
                  </>
                )}
              </Button>

              {/* Mute Button */}
              <Button
                variant={isMuted ? "destructive" : "mute"}
                size="control"
                onClick={toggleMute}
                disabled={!isCallActive}
              >
                {isMuted ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
              </Button>

              {/* Camera Button */}
              <Button
                variant={isCameraOn ? "camera" : "outline"}
                size="control"
                onClick={toggleCamera}
                disabled={!isCallActive}
              >
                {isCameraOn ? <Camera className="w-6 h-6" /> : <CameraOff className="w-6 h-6" />}
              </Button>
            </div>

            {/* Advanced Camera Controls */}
            {isCameraOn && isCallActive && (
              <div className="space-y-4">
                <Button
                  variant="secondary"
                  size="default"
                  onClick={switchCamera}
                  className="w-full"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  {agoraService.getCurrentFacingMode() === "environment" ? "SWITCH TO FRONT" : "SWITCH TO REAR"}
                </Button>
              </div>
            )}
          </Card>

          {/* Squadron Status */}
          <Card className="border-4 border-border bg-card p-6">
            <h3 className="text-xl font-black mb-4">SQUADRON STATUS</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="font-bold">COMMS:</span>
                <span className={`font-bold ${isMuted ? 'text-destructive' : 'text-success'}`}>
                  {isMuted ? 'SILENT' : 'ACTIVE'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-bold">VISUAL:</span>
                <span className={`font-bold ${isCameraOn ? 'text-success' : 'text-muted-foreground'}`}>
                  {isCameraOn ? 'ONLINE' : 'OFFLINE'}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="font-bold">CAMERA:</span>
                <span className="font-bold text-foreground">
                  {agoraService.getCurrentFacingMode() === "environment" ? 'EXTERNAL' : 'COCKPIT'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="font-bold">CONNECTION:</span>
                <span className={`font-bold ${isConnected ? 'text-success' : 'text-destructive'}`}>
                  {isConnected ? 'SECURED' : 'DISCONNECTED'}
                </span>
              </div>

              {isCallActive && (
                <>
                  <div className="flex justify-between items-center">
                    <span className="font-bold">SQUADRON:</span>
                    <span className="font-bold text-success">{totalParticipants} PILOTS</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="font-bold">FORMATION:</span>
                    <span className="font-bold text-accent">
                      {totalParticipants === 1 && "SOLO"}
                      {totalParticipants === 2 && "PAIR"}
                      {totalParticipants === 3 && "TRIANGLE"}
                      {totalParticipants === 4 && "DIAMOND"}
                      {totalParticipants === 5 && "V-WING"}
                      {totalParticipants > 5 && "FLEET"}
                    </span>
                  </div>
                </>
              )}
            </div>
          </Card>

          {/* Advanced Settings */}
          <Card className="border-4 border-border bg-card p-6">
            <h3 className="text-xl font-black mb-4">SYSTEM CONFIG</h3>
            
            <div className="space-y-3">
              <Button variant="outline" size="default" className="w-full">
                <Settings className="w-4 h-4 mr-2" />
                ADVANCED SETTINGS
              </Button>
              
              <div className="text-xs text-muted-foreground space-y-1">
                <div>APP ID: {agoraConfig.appId.slice(0, 8)}...</div>
                <div>CHANNEL: {agoraConfig.channel}</div>
                <div>PILOT ID: {agoraConfig.uid}</div>
                {currentToken && (
                  <div>TOKEN: ACTIVE</div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Mobile Control Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-card border-t-4 border-border p-4 lg:hidden">
        <div className="flex justify-center gap-4">
          <Button
            variant={isCallActive ? "destructive" : "call"}
            size="control"
            onClick={handleCall}
          >
            {isCallActive ? <PhoneOff className="w-6 h-6" /> : <Phone className="w-6 h-6" />}
          </Button>
          
          <Button
            variant={isMuted ? "destructive" : "mute"}
            size="control"
            onClick={toggleMute}
            disabled={!isCallActive}
          >
            {isMuted ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
          </Button>
          
          <Button
            variant={isCameraOn ? "camera" : "outline"}
            size="control"
            onClick={toggleCamera}
            disabled={!isCallActive}
          >
            {isCameraOn ? <Camera className="w-6 h-6" /> : <CameraOff className="w-6 h-6" />}
          </Button>

          {isCameraOn && isCallActive && (
            <Button
              variant="secondary"
              size="control"
              onClick={switchCamera}
            >
              <RotateCcw className="w-6 h-6" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};