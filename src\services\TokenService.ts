// Token service for managing Agora temporary tokens
export interface TokenConfig {
  appId: string;
  appCertificate?: string;
  channelName: string;
  uid: number;
  expireTime?: number;
}

export class TokenService {
  private static readonly DEFAULT_EXPIRE_TIME = 3600; // 1 hour in seconds

  /**
   * Generate a temporary token for demo purposes
   * In production, this should call your backend token server
   */
  public static generateTempToken(config: TokenConfig): string {
    const currentTime = Math.floor(Date.now() / 1000);
    const expireTime = config.expireTime || currentTime + this.DEFAULT_EXPIRE_TIME;
    
    // For demo purposes - in production, use proper Agora RTC Token generation
    // This should be replaced with actual token generation using Agora's server SDK
    const tokenData = {
      appId: config.appId,
      channel: config.channelName,
      uid: config.uid,
      expire: expireTime,
      timestamp: currentTime
    };
    
    // Base64 encode the token data for demo
    const tokenString = btoa(JSON.stringify(tokenData));
    return `temp_token_${tokenString}`;
  }

  /**
   * Validate if a token is still valid
   */
  public static validateToken(token: string): boolean {
    try {
      if (!token.startsWith('temp_token_')) return false;
      
      const tokenData = JSON.parse(atob(token.replace('temp_token_', '')));
      const currentTime = Math.floor(Date.now() / 1000);
      
      return tokenData.expire > currentTime;
    } catch {
      return false;
    }
  }

  /**
   * Get token expiration time
   */
  public static getTokenExpiration(token: string): number | null {
    try {
      if (!token.startsWith('temp_token_')) return null;
      
      const tokenData = JSON.parse(atob(token.replace('temp_token_', '')));
      return tokenData.expire;
    } catch {
      return null;
    }
  }

  /**
   * Check if token needs renewal (expires within 5 minutes)
   */
  public static needsRenewal(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    const fiveMinutes = 5 * 60;
    
    return (expiration - currentTime) < fiveMinutes;
  }

  /**
   * Renew token with new expiration time
   */
  public static renewToken(config: TokenConfig): string {
    return this.generateTempToken({
      ...config,
      expireTime: Math.floor(Date.now() / 1000) + this.DEFAULT_EXPIRE_TIME
    });
  }
}