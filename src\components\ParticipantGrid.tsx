import { useEffect, useRef } from "react";
import { RemoteUser } from "@/services/AgoraService";
import { Users, Mic, MicOff, <PERSON>, CameraOff } from "lucide-react";

interface ParticipantGridProps {
  participants: RemoteUser[];
  localVideoRef?: React.RefObject<HTMLDivElement>;
  onParticipantVideoPlay?: (uid: number, element: HTMLElement) => void;
  maxParticipants?: number;
}

export const ParticipantGrid = ({ 
  participants, 
  localVideoRef, 
  onParticipantVideoPlay,
  maxParticipants = 12 
}: ParticipantGridProps) => {
  const gridRefs = useRef<Map<number, HTMLDivElement>>(new Map());

  // Setup video elements for remote users
  useEffect(() => {
    participants.forEach(participant => {
      const element = gridRefs.current.get(participant.uid);
      if (element && participant.hasVideo && onParticipantVideoPlay) {
        onParticipantVideoPlay(participant.uid, element);
      }
    });
  }, [participants, onParticipantVideoPlay]);

  // Spaceship formation layout calculation
  const getSpaceshipLayout = (count: number) => {
    if (count <= 1) return { rows: 1, cols: 1 };
    if (count <= 2) return { rows: 1, cols: 2 };
    if (count <= 4) return { rows: 2, cols: 2 };
    if (count <= 6) return { rows: 2, cols: 3 };
    if (count <= 9) return { rows: 3, cols: 3 };
    if (count <= 12) return { rows: 3, cols: 4 };
    return { rows: 4, cols: 4 };
  };

  const totalParticipants = Math.min(participants.length + 1, maxParticipants); // +1 for local user
  const layout = getSpaceshipLayout(totalParticipants);

  // Spaceship formation positioning
  const getSpaceshipPosition = (index: number, total: number) => {
    const { rows, cols } = getSpaceshipLayout(total);
    
    // Special formations for different counts
    if (total === 1) return { row: 0, col: 0 };
    
    if (total === 2) {
      return { row: 0, col: index };
    }
    
    if (total === 3) {
      // Triangle formation
      const formations = [
        { row: 0, col: 1 }, // Leader
        { row: 1, col: 0 }, // Left wing
        { row: 1, col: 2 }, // Right wing
      ];
      return formations[index] || { row: 0, col: 0 };
    }
    
    if (total === 4) {
      // Diamond formation
      const formations = [
        { row: 0, col: 1 }, // Top
        { row: 1, col: 0 }, // Left
        { row: 1, col: 2 }, // Right
        { row: 2, col: 1 }, // Bottom
      ];
      return formations[index] || { row: 0, col: 0 };
    }
    
    if (total === 5) {
      // V formation
      const formations = [
        { row: 0, col: 2 }, // Leader
        { row: 1, col: 1 }, // Left wing 1
        { row: 1, col: 3 }, // Right wing 1
        { row: 2, col: 0 }, // Left wing 2
        { row: 2, col: 4 }, // Right wing 2
      ];
      return formations[index] || { row: 0, col: 0 };
    }
    
    // Standard grid for larger groups
    const row = Math.floor(index / cols);
    const col = index % cols;
    return { row, col };
  };

  return (
    <div className="w-full h-full">
      {totalParticipants === 1 ? (
        // Single participant - full screen
        <div className="w-full h-full">
          <div 
            ref={localVideoRef}
            className="w-full h-full bg-primary border-2 border-border flex items-center justify-center relative group"
          >
            <Camera className="w-16 h-16 text-primary-foreground" />
            <div className="absolute bottom-4 left-4 bg-success text-success-foreground px-3 py-1 border border-border text-sm font-bold">
              YOU (LOCAL)
            </div>
          </div>
        </div>
      ) : (
        // Multiple participants - spaceship formation
        <div 
          className="w-full h-full relative"
          style={{
            display: 'grid',
            gridTemplateRows: `repeat(${layout.rows}, 1fr)`,
            gridTemplateColumns: `repeat(${layout.cols}, 1fr)`,
            gap: '8px',
            padding: '8px'
          }}
        >
          {/* Local video (always first position - leader) */}
          <div
            ref={localVideoRef}
            className="bg-primary border-2 border-border flex items-center justify-center relative group min-h-[120px] aspect-video"
            style={{
              gridRow: getSpaceshipPosition(0, totalParticipants).row + 1,
              gridColumn: getSpaceshipPosition(0, totalParticipants).col + 1,
            }}
          >
            <Camera className="w-8 h-8 text-primary-foreground" />
            <div className="absolute bottom-2 left-2 bg-success text-success-foreground px-2 py-1 border border-border text-xs font-bold">
              YOU
            </div>
            
            {/* Spaceship leader indicator */}
            <div className="absolute top-2 left-2 bg-accent text-accent-foreground p-1 border border-border">
              <div className="w-2 h-2 bg-accent-foreground"></div>
            </div>
          </div>

          {/* Remote participants */}
          {participants.slice(0, maxParticipants - 1).map((participant, index) => {
            const position = getSpaceshipPosition(index + 1, totalParticipants);
            
            return (
              <div
                key={participant.uid}
                ref={(el) => {
                  if (el) {
                    gridRefs.current.set(participant.uid, el);
                  }
                }}
                className="bg-muted border-2 border-border flex items-center justify-center relative group min-h-[120px] aspect-video"
                style={{
                  gridRow: position.row + 1,
                  gridColumn: position.col + 1,
                }}
              >
                {participant.hasVideo ? (
                  <div className="w-full h-full bg-primary"></div>
                ) : (
                  <Users className="w-8 h-8 text-muted-foreground" />
                )}
                
                {/* Participant info */}
                <div className="absolute bottom-2 left-2 bg-card text-card-foreground px-2 py-1 border border-border text-xs font-bold">
                  USER {participant.uid}
                </div>
                
                {/* Audio/Video status indicators */}
                <div className="absolute top-2 right-2 flex gap-1">
                  {participant.hasAudio ? (
                    <div className="bg-success text-success-foreground p-1 border border-border">
                      <Mic className="w-3 h-3" />
                    </div>
                  ) : (
                    <div className="bg-destructive text-destructive-foreground p-1 border border-border">
                      <MicOff className="w-3 h-3" />
                    </div>
                  )}
                  
                  {participant.hasVideo ? (
                    <div className="bg-success text-success-foreground p-1 border border-border">
                      <Camera className="w-3 h-3" />
                    </div>
                  ) : (
                    <div className="bg-muted text-muted-foreground p-1 border border-border">
                      <CameraOff className="w-3 h-3" />
                    </div>
                  )}
                </div>

                {/* Wing formation indicators */}
                <div className="absolute top-2 left-2 bg-secondary text-secondary-foreground p-1 border border-border">
                  <div className="w-2 h-2 bg-secondary-foreground"></div>
                </div>
              </div>
            );
          })}

          {/* Formation indicators overlay */}
          {totalParticipants >= 3 && (
            <div className="absolute top-4 left-4 bg-card text-card-foreground px-3 py-2 border-2 border-border">
              <div className="text-xs font-bold">
                SPACESHIP FORMATION
              </div>
              <div className="text-xs text-muted-foreground">
                {totalParticipants === 3 && "TRIANGLE"}
                {totalParticipants === 4 && "DIAMOND"}  
                {totalParticipants === 5 && "V-FORMATION"}
                {totalParticipants > 5 && `SQUADRON ${totalParticipants}`}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};